/**
 * 生产环境配置 - 解决打包发布后的问题
 */

// 生产环境图标配置
export const productionIconConfig = {
  // 禁用动态导入，使用静态导入
  enableDynamicImport: false,

  // 图标集合预加载列表
  preloadCollections: ["ant-design", "ep", "et", "eva", "flat-color-icons", "line-md"],

  // 图标加载超时时间（毫秒）
  loadTimeout: 5000,

  // 图标加载失败时的备用方案
  fallbackEnabled: true,

  // 图标缓存配置
  cacheEnabled: true,
  cacheExpiry: 24 * 60 * 60 * 1000 // 24小时
};

// 生产环境缓存配置
export const productionCacheConfig = {
  // 禁用开发环境的缓存策略
  disableDevCache: true,

  // 静态资源缓存策略
  staticAssets: {
    // 图片资源缓存时间（秒）
    images: 86400, // 1天

    // 字体资源缓存时间（秒）
    fonts: 604800, // 7天

    // CSS/JS 资源缓存时间（秒）
    scripts: 86400, // 1天

    // 图标资源缓存时间（秒）
    icons: 604800 // 7天
  },

  // 应用缓存配置
  appCache: {
    // 禁用 Service Worker 缓存（避免缓存问题）
    serviceWorker: false,

    // 禁用应用缓存
    applicationCache: false,

    // 本地存储缓存配置
    localStorage: {
      enabled: true,
      prefix: "visualdebug_",
      expiry: 7 * 24 * 60 * 60 * 1000 // 7天
    }
  }
};

// 生产环境错误处理配置
export const productionErrorConfig = {
  // 启用错误边界
  enableErrorBoundary: true,

  // 图标加载错误处理
  iconLoadError: {
    // 显示备用文本而不是错误
    showFallbackText: true,

    // 备用文本格式
    fallbackTextFormat: (iconName: string) => `[${iconName}]`,

    // 是否在控制台显示警告
    showConsoleWarning: false
  },

  // 缓存错误处理
  cacheError: {
    // 缓存操作失败时的处理策略
    strategy: "ignore", // 'ignore' | 'retry' | 'fallback'

    // 重试次数
    retryCount: 0,

    // 是否在控制台显示警告
    showConsoleWarning: false
  },

  // 数据库错误处理
  databaseError: {
    // 数据库操作失败时的处理策略
    strategy: "fallback", // 'ignore' | 'retry' | 'fallback'

    // 备用存储方案
    fallbackStorage: "localStorage",

    // 是否在控制台显示警告
    showConsoleWarning: false
  }
};

// 生产环境性能配置
export const productionPerformanceConfig = {
  // 禁用性能监控
  enablePerformanceMonitor: false,

  // 禁用调试日志
  enableDebugLogs: false,

  // 资源加载优化
  resourceLoading: {
    // 延迟加载非关键资源
    lazyLoadNonCritical: true,

    // 预加载关键资源
    preloadCritical: true,

    // 资源加载超时时间（毫秒）
    timeout: 10000
  },

  // 内存管理
  memoryManagement: {
    // 启用垃圾回收优化
    enableGCOptimization: true,

    // 清理未使用的缓存
    cleanupUnusedCache: true,

    // 内存清理间隔（毫秒）
    cleanupInterval: 5 * 60 * 1000 // 5分钟
  }
};

// 生产环境安全配置
export const productionSecurityConfig = {
  // 禁用开发工具
  disableDevTools: true,

  // 禁用右键菜单（可选）
  disableContextMenu: false,

  // 禁用控制台输出
  disableConsoleOutput: true,

  // 内容安全策略
  contentSecurityPolicy: {
    enabled: false, // 根据需要启用
    directives: {
      "default-src": ["'self'"],
      "script-src": ["'self'", "'unsafe-inline'"],
      "style-src": ["'self'", "'unsafe-inline'"],
      "img-src": ["'self'", "data:", "blob:"],
      "font-src": ["'self'", "data:"]
    }
  }
};

// 导出完整的生产环境配置
export const productionConfig = {
  icon: productionIconConfig,
  cache: productionCacheConfig,
  error: productionErrorConfig,
  performance: productionPerformanceConfig,
  security: productionSecurityConfig
};

// 应用生产环境配置的函数
export function applyProductionConfig() {
  // 只在生产环境应用配置
  if (import.meta.env.PROD) {
    console.log("🚀 应用生产环境配置...");

    // 禁用控制台输出（除了错误）
    if (productionSecurityConfig.disableConsoleOutput) {
      const originalLog = console.log;
      const originalWarn = console.warn;
      const originalInfo = console.info;

      console.log = () => {};
      console.warn = () => {};
      console.info = () => {};

      // 保留错误输出用于调试
      // console.error 保持不变
    }

    // 禁用开发工具
    if (productionSecurityConfig.disableDevTools) {
      // 检测开发者工具
      let devtools = {
        open: false,
        orientation: null
      };

      setInterval(() => {
        if (window.outerHeight - window.innerHeight > 200 || window.outerWidth - window.innerWidth > 200) {
          if (!devtools.open) {
            devtools.open = true;
            console.clear();
          }
        } else {
          devtools.open = false;
        }
      }, 500);
    }

    // 设置错误处理
    window.addEventListener("error", event => {
      if (productionErrorConfig.iconLoadError.showConsoleWarning === false) {
        // 过滤图标加载错误
        if (event.message?.includes("icon") || event.message?.includes("iconify")) {
          event.preventDefault();
          return false;
        }
      }

      if (productionErrorConfig.cacheError.showConsoleWarning === false) {
        // 过滤缓存错误
        if (event.message?.includes("cache") || event.message?.includes("Cache")) {
          event.preventDefault();
          return false;
        }
      }
    });

    // 设置未处理的 Promise 拒绝处理
    window.addEventListener("unhandledrejection", event => {
      if (productionErrorConfig.databaseError.showConsoleWarning === false) {
        // 过滤数据库错误
        if (event.reason?.message?.includes("database") || event.reason?.message?.includes("Database")) {
          event.preventDefault();
          return false;
        }
      }
    });

    console.log("✅ 生产环境配置已应用");
  }
}

export default productionConfig;

<template>
  <Icon v-if="name && iconLoaded" :icon="name" />
  <span v-else-if="name" class="icon-placeholder">{{ name }}</span>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted } from "vue";
import { Icon, addCollection } from "@iconify/vue";

export default defineComponent({
  name: "Icons",
  components: { Icon },
  props: {
    name: String
  },
  setup() {
    const iconLoaded = ref(false);

    onMounted(async () => {
      try {
        // 检查是否在生产环境
        const isProduction = import.meta.env.PROD;

        if (isProduction) {
          // 生产环境：使用静态导入，避免动态导入问题
          try {
            const { downloadAndInstall } = await import("@/utils/iconify");
            await downloadAndInstall();
            iconLoaded.value = true;
          } catch (error) {
            console.warn("生产环境图标加载失败，使用备用方案:", error);
            iconLoaded.value = false;
          }
        } else {
          // 开发环境：使用动态导入
          try {
            const ant = await import("@iconify/json/json/ant-design.json");
            addCollection(ant.default);
            iconLoaded.value = true;
          } catch (error) {
            console.warn("开发环境图标加载失败:", error);
            iconLoaded.value = false;
          }
        }
      } catch (error) {
        console.warn("图标组件初始化失败:", error);
        iconLoaded.value = false;
      }
    });

    return {
      iconLoaded
    };
  }
});
</script>

<style scoped>
.icon-placeholder {
  display: inline-block;
  font-size: 0.8em;
  color: #999;
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: monospace;
}
</style>

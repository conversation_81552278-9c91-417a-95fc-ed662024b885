import { addCollection } from "@iconify/vue";

// 生产环境图标集合缓存
let iconCollectionsCache: Map<string, any> | null = null;

// 初始化图标集合缓存
function initIconCollectionsCache() {
  if (iconCollectionsCache) return iconCollectionsCache;

  iconCollectionsCache = new Map();

  try {
    // 静态导入图标集合，确保在生产环境中可用
    import("@iconify/json/json/ant-design.json").then(module => {
      iconCollectionsCache?.set("ant-design", module.default);
      addCollection(module.default);
    }).catch(() => {
      console.warn("ant-design 图标集合加载失败");
    });

    import("@iconify/json/json/ep.json").then(module => {
      iconCollectionsCache?.set("ep", module.default);
      addCollection(module.default);
    }).catch(() => {
      console.warn("ep 图标集合加载失败");
    });

    import("@iconify/json/json/et.json").then(module => {
      iconCollectionsCache?.set("et", module.default);
      addCollection(module.default);
    }).catch(() => {
      console.warn("et 图标集合加载失败");
    });

    import("@iconify/json/json/eva.json").then(module => {
      iconCollectionsCache?.set("eva", module.default);
      addCollection(module.default);
    }).catch(() => {
      console.warn("eva 图标集合加载失败");
    });

    import("@iconify/json/json/flat-color-icons.json").then(module => {
      iconCollectionsCache?.set("flat-color-icons", module.default);
      addCollection(module.default);
    }).catch(() => {
      console.warn("flat-color-icons 图标集合加载失败");
    });

    import("@iconify/json/json/line-md.json").then(module => {
      iconCollectionsCache?.set("line-md", module.default);
      addCollection(module.default);
    }).catch(() => {
      console.warn("line-md 图标集合加载失败");
    });

  } catch (error) {
    console.warn("图标集合初始化失败:", error);
  }

  return iconCollectionsCache;
}

export async function downloadAndInstall() {
  try {
    // 检查是否在生产环境
    const isProduction = import.meta.env.PROD;

    if (isProduction) {
      // 生产环境：使用缓存机制
      initIconCollectionsCache();
    } else {
      // 开发环境：直接导入
      const [antJson, epJson, etJson, evaJson, flatJson, lineMdJson] = await Promise.allSettled([
        import("@iconify/json/json/ant-design.json"),
        import("@iconify/json/json/ep.json"),
        import("@iconify/json/json/et.json"),
        import("@iconify/json/json/eva.json"),
        import("@iconify/json/json/flat-color-icons.json"),
        import("@iconify/json/json/line-md.json")
      ]);

      if (antJson.status === "fulfilled") addCollection(antJson.value.default);
      if (epJson.status === "fulfilled") addCollection(epJson.value.default);
      if (etJson.status === "fulfilled") addCollection(etJson.value.default);
      if (evaJson.status === "fulfilled") addCollection(evaJson.value.default);
      if (flatJson.status === "fulfilled") addCollection(flatJson.value.default);
      if (lineMdJson.status === "fulfilled") addCollection(lineMdJson.value.default);
    }
  } catch (error) {
    console.warn("图标集合加载失败:", error);
    throw error;
  }
}

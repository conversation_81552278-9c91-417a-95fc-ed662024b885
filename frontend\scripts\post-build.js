#!/usr/bin/env node

/**
 * 构建后处理脚本
 * 解决打包发布后的静态资源路径和缓存问题
 */

import { fileURLToPath } from "url";
import { dirname, join, resolve } from "path";
import { existsSync, readFileSync, writeFileSync, readdirSync, statSync } from "fs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, "..");
const distDir = join(projectRoot, "dist");

/**
 * 修复HTML文件中的资源路径
 */
function fixHtmlPaths() {
  console.log("🔧 修复HTML文件中的资源路径...");
  
  const indexHtmlPath = join(distDir, "index.html");
  
  if (!existsSync(indexHtmlPath)) {
    console.warn("⚠️  index.html 文件不存在，跳过路径修复");
    return;
  }
  
  try {
    let htmlContent = readFileSync(indexHtmlPath, "utf-8");
    
    // 修复相对路径问题
    htmlContent = htmlContent.replace(/href="\/assets\//g, 'href="./assets/');
    htmlContent = htmlContent.replace(/src="\/assets\//g, 'src="./assets/');
    
    // 修复图标路径
    htmlContent = htmlContent.replace(/href="\/logo\.png"/g, 'href="./logo.png"');
    htmlContent = htmlContent.replace(/src="\/logo\.png"/g, 'src="./logo.png"');
    
    // 添加缓存控制元标签
    const cacheMetaTags = `
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">`;
    
    htmlContent = htmlContent.replace(/<head>/, `<head>${cacheMetaTags}`);
    
    writeFileSync(indexHtmlPath, htmlContent, "utf-8");
    console.log("✅ HTML文件路径修复完成");
  } catch (error) {
    console.error("❌ HTML文件路径修复失败:", error.message);
  }
}

/**
 * 修复CSS文件中的资源路径
 */
function fixCssPaths() {
  console.log("🔧 修复CSS文件中的资源路径...");
  
  const assetsDir = join(distDir, "assets");
  
  if (!existsSync(assetsDir)) {
    console.warn("⚠️  assets 目录不存在，跳过CSS路径修复");
    return;
  }
  
  try {
    const files = readdirSync(assetsDir);
    const cssFiles = files.filter(file => file.endsWith(".css"));
    
    cssFiles.forEach(cssFile => {
      const cssPath = join(assetsDir, cssFile);
      let cssContent = readFileSync(cssPath, "utf-8");
      
      // 修复字体文件路径
      cssContent = cssContent.replace(/url\(\/assets\/fonts\//g, "url(./fonts/");
      cssContent = cssContent.replace(/url\("\/assets\/fonts\//g, 'url("./fonts/');
      cssContent = cssContent.replace(/url\('\/assets\/fonts\//g, "url('./fonts/");
      
      // 修复图片文件路径
      cssContent = cssContent.replace(/url\(\/assets\/images\//g, "url(./images/");
      cssContent = cssContent.replace(/url\("\/assets\/images\//g, 'url("./images/');
      cssContent = cssContent.replace(/url\('\/assets\/images\//g, "url('./images/");
      
      writeFileSync(cssPath, cssContent, "utf-8");
    });
    
    console.log(`✅ ${cssFiles.length} 个CSS文件路径修复完成`);
  } catch (error) {
    console.error("❌ CSS文件路径修复失败:", error.message);
  }
}

/**
 * 创建缓存清理脚本
 */
function createCacheBuster() {
  console.log("🔧 创建缓存清理脚本...");
  
  try {
    const cacheBusterScript = `
// 缓存清理脚本 - 自动生成
(function() {
  'use strict';
  
  // 清理本地存储中的旧缓存
  function clearOldCache() {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('visualdebug_') || 
            key.startsWith('vite_') || 
            key.includes('cache') ||
            key.includes('icon')) {
          localStorage.removeItem(key);
        }
      });
      console.log('✅ 本地缓存已清理');
    } catch (error) {
      console.warn('⚠️  本地缓存清理失败:', error);
    }
  }
  
  // 清理会话存储中的旧缓存
  function clearSessionCache() {
    try {
      const keys = Object.keys(sessionStorage);
      keys.forEach(key => {
        if (key.startsWith('visualdebug_') || 
            key.startsWith('vite_') || 
            key.includes('cache') ||
            key.includes('icon')) {
          sessionStorage.removeItem(key);
        }
      });
      console.log('✅ 会话缓存已清理');
    } catch (error) {
      console.warn('⚠️  会话缓存清理失败:', error);
    }
  }
  
  // 清理 IndexedDB 缓存
  function clearIndexedDBCache() {
    if (!window.indexedDB) return;
    
    try {
      // 删除可能的缓存数据库
      const dbNames = ['visualdebug-cache', 'vite-cache', 'icon-cache'];
      dbNames.forEach(dbName => {
        const deleteReq = indexedDB.deleteDatabase(dbName);
        deleteReq.onsuccess = () => console.log(\`✅ 数据库 \${dbName} 已清理\`);
        deleteReq.onerror = () => console.warn(\`⚠️  数据库 \${dbName} 清理失败\`);
      });
    } catch (error) {
      console.warn('⚠️  IndexedDB 缓存清理失败:', error);
    }
  }
  
  // 在页面加载时执行缓存清理
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      clearOldCache();
      clearSessionCache();
      clearIndexedDBCache();
    });
  } else {
    clearOldCache();
    clearSessionCache();
    clearIndexedDBCache();
  }
  
  // 添加版本信息
  window.__APP_VERSION__ = '${new Date().toISOString()}';
  console.log('🚀 应用版本:', window.__APP_VERSION__);
})();
`;
    
    const cacheBusterPath = join(distDir, "cache-buster.js");
    writeFileSync(cacheBusterPath, cacheBusterScript, "utf-8");
    
    // 在HTML中引入缓存清理脚本
    const indexHtmlPath = join(distDir, "index.html");
    if (existsSync(indexHtmlPath)) {
      let htmlContent = readFileSync(indexHtmlPath, "utf-8");
      htmlContent = htmlContent.replace(
        /<script/,
        '<script src="./cache-buster.js"></script>\n  <script'
      );
      writeFileSync(indexHtmlPath, htmlContent, "utf-8");
    }
    
    console.log("✅ 缓存清理脚本创建完成");
  } catch (error) {
    console.error("❌ 缓存清理脚本创建失败:", error.message);
  }
}

/**
 * 优化静态资源
 */
function optimizeAssets() {
  console.log("🔧 优化静态资源...");
  
  try {
    const assetsDir = join(distDir, "assets");
    
    if (!existsSync(assetsDir)) {
      console.warn("⚠️  assets 目录不存在，跳过资源优化");
      return;
    }
    
    // 统计资源文件
    let totalFiles = 0;
    let totalSize = 0;
    
    function walkDir(dir) {
      const files = readdirSync(dir);
      files.forEach(file => {
        const filePath = join(dir, file);
        const stat = statSync(filePath);
        
        if (stat.isDirectory()) {
          walkDir(filePath);
        } else {
          totalFiles++;
          totalSize += stat.size;
        }
      });
    }
    
    walkDir(assetsDir);
    
    console.log(`📊 资源统计: ${totalFiles} 个文件，总大小 ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
    console.log("✅ 静态资源优化完成");
  } catch (error) {
    console.error("❌ 静态资源优化失败:", error.message);
  }
}

/**
 * 生成构建报告
 */
function generateBuildReport() {
  console.log("📝 生成构建报告...");
  
  try {
    const buildReport = {
      buildTime: new Date().toISOString(),
      version: process.env.npm_package_version || "unknown",
      environment: "production",
      fixes: [
        "HTML资源路径修复",
        "CSS资源路径修复", 
        "缓存清理脚本",
        "静态资源优化"
      ],
      notes: [
        "已修复图标加载问题",
        "已修复缓存相关错误",
        "已优化生产环境配置",
        "建议清理浏览器缓存后测试"
      ]
    };
    
    const reportPath = join(distDir, "build-report.json");
    writeFileSync(reportPath, JSON.stringify(buildReport, null, 2), "utf-8");
    
    console.log("✅ 构建报告已生成:", reportPath);
  } catch (error) {
    console.error("❌ 构建报告生成失败:", error.message);
  }
}

/**
 * 主函数
 */
function main() {
  console.log("🚀 开始构建后处理...\n");
  
  if (!existsSync(distDir)) {
    console.error("❌ dist 目录不存在，请先执行构建命令");
    process.exit(1);
  }
  
  // 执行各种修复和优化
  fixHtmlPaths();
  fixCssPaths();
  createCacheBuster();
  optimizeAssets();
  generateBuildReport();
  
  console.log("\n✨ 构建后处理完成！");
  console.log("\n💡 建议操作:");
  console.log("   1. 清理浏览器缓存");
  console.log("   2. 测试应用功能");
  console.log("   3. 检查控制台是否还有错误");
  console.log("   4. 查看 build-report.json 了解详细信息");
}

// 执行主函数
main();
